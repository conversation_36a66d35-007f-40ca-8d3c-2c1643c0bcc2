#!/usr/bin/env ts-node

import { Client } from 'pg';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

interface NetworkData {
  network_name: string;
  contact_phone?: string;
  contact_email?: string;
  resource_url?: string;
  notes?: string;
}

interface NetworkRelationship {
  network_name: string;
  carrier_name: string;
  effective_date?: string;
  termination_date?: string;
  special_notes?: string;
  verification_required?: boolean;
}

class NetworkRelationshipManager {
  private createClient(): Client {
    return new Client({
      connectionString: process.env.POSTGRES_CONNECTION_STRING,
      ssl: { rejectUnauthorized: false },
    });
  }

  async run() {
    console.log('🌐 Populating network relationships...');
    
    try {
      await this.createNetworks();
      await this.createNetworkRelationships();
      
      console.log('✅ Network relationships populated successfully!');
      
    } catch (error) {
      console.error('❌ Error populating network relationships:', error);
      process.exit(1);
    }
  }

  private async createNetworks() {
    console.log('📡 Creating insurance networks...');
    
    const client = this.createClient();
    await client.connect();
    
    try {
      const networks: NetworkData[] = [
        {
          network_name: 'Delta Dental PPO',
          contact_phone: '**************',
          resource_url: 'https://www.deltadentalins.com',
          notes: 'Largest dental benefits carrier in the US'
        },
        {
          network_name: 'Delta Dental Premier',
          contact_phone: '**************',
          resource_url: 'https://www.deltadentalins.com',
          notes: 'Traditional fee-for-service network'
        },
        {
          network_name: 'DeltaCare USA (DMO)',
          contact_phone: '**************',
          resource_url: 'https://www.deltadentalins.com',
          notes: 'Dental maintenance organization network'
        },
        {
          network_name: 'Cigna DPPO',
          contact_phone: '**************',
          resource_url: 'https://www.cigna.com/dental',
          notes: 'Cigna Dental PPO network'
        },
        {
          network_name: 'Cigna DHMO',
          contact_phone: '**************',
          resource_url: 'https://www.cigna.com/dental',
          notes: 'Cigna Dental HMO network'
        },
        {
          network_name: 'Aetna Dental PPO',
          contact_phone: '**************',
          resource_url: 'https://www.aetna.com/dental',
          notes: 'Aetna preferred provider organization'
        },
        {
          network_name: 'Aetna DMO',
          contact_phone: '**************',
          resource_url: 'https://www.aetna.com/dental',
          notes: 'Aetna dental maintenance organization'
        },
        {
          network_name: 'Humana Dental PPO',
          contact_phone: '**************',
          resource_url: 'https://www.humana.com/dental',
          notes: 'Humana preferred provider network'
        },
        {
          network_name: 'United Concordia PPO',
          contact_phone: '**************',
          resource_url: 'https://www.unitedconcordia.com',
          notes: 'United Concordia preferred provider organization'
        },
        {
          network_name: 'Guardian DentalGuard',
          contact_phone: '**************',
          resource_url: 'https://www.guardianlife.com/dental',
          notes: 'Guardian preferred provider network'
        },
        {
          network_name: 'MetLife PDP Plus',
          contact_phone: '**************',
          resource_url: 'https://www.metlife.com/dental',
          notes: 'MetLife preferred dentist program'
        },
        {
          network_name: 'Blue Cross Blue Shield Dental',
          contact_phone: 'Varies by state',
          resource_url: 'https://www.bcbs.com',
          notes: 'State-specific BCBS dental networks'
        },
        {
          network_name: 'Anthem Dental PPO',
          contact_phone: '1-************',
          resource_url: 'https://www.anthem.com/dental',
          notes: 'Anthem preferred provider organization'
        },
        {
          network_name: 'GEHA Dental',
          contact_phone: '1-************',
          resource_url: 'https://www.geha.com/dental',
          notes: 'Government Employees Health Association'
        },
        {
          network_name: 'FEDVIP Dental',
          contact_phone: 'Varies by carrier',
          resource_url: 'https://www.opm.gov/healthcare-insurance/dental-vision/',
          notes: 'Federal Employee Dental and Vision Insurance Program'
        }
      ];

      // Insert networks
      for (const network of networks) {
        await client.query(`
          INSERT INTO insurance_networks (network_name, contact_phone, contact_email, resource_url, notes, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
          ON CONFLICT (network_name) DO NOTHING
        `, [
          network.network_name,
          network.contact_phone,
          network.contact_email,
          network.resource_url,
          network.notes
        ]);
      }

      console.log(`  ✅ Created ${networks.length} insurance networks`);
      
    } finally {
      await client.end();
    }
  }

  private async createNetworkRelationships() {
    console.log('🔗 Creating network-carrier relationships...');
    
    const client = this.createClient();
    await client.connect();
    
    try {
      const relationships: NetworkRelationship[] = [
        // Delta Dental relationships
        { network_name: 'Delta Dental PPO', carrier_name: 'Delta Dental', effective_date: '2020-01-01', verification_required: false },
        { network_name: 'Delta Dental Premier', carrier_name: 'Delta Dental', effective_date: '2020-01-01', verification_required: false },
        { network_name: 'DeltaCare USA (DMO)', carrier_name: 'Delta Dental', effective_date: '2020-01-01', verification_required: true },
        
        // State-specific Delta relationships
        { network_name: 'Delta Dental PPO', carrier_name: 'Delta Dental of California', effective_date: '2020-01-01' },
        { network_name: 'Delta Dental PPO', carrier_name: 'Delta Dental of Texas', effective_date: '2020-01-01' },
        { network_name: 'Delta Dental PPO', carrier_name: 'Delta Dental of Florida', effective_date: '2020-01-01' },
        { network_name: 'Delta Dental PPO', carrier_name: 'Delta Dental of New York', effective_date: '2020-01-01' },
        { network_name: 'Delta Dental PPO', carrier_name: 'Delta Dental of Illinois', effective_date: '2020-01-01' },
        
        // Cigna relationships
        { network_name: 'Cigna DPPO', carrier_name: 'Cigna', effective_date: '2020-01-01', verification_required: false },
        { network_name: 'Cigna DHMO', carrier_name: 'Cigna', effective_date: '2020-01-01', verification_required: true },
        { network_name: 'Cigna DPPO', carrier_name: 'CIGNA', effective_date: '2020-01-01' },
        
        // Aetna relationships
        { network_name: 'Aetna Dental PPO', carrier_name: 'Aetna Dental Plans', effective_date: '2020-01-01', verification_required: false },
        { network_name: 'Aetna DMO', carrier_name: 'Aetna Dental Plans', effective_date: '2020-01-01', verification_required: true },
        
        // Humana relationships
        { network_name: 'Humana Dental PPO', carrier_name: 'Humana', effective_date: '2020-01-01', verification_required: false },
        { network_name: 'Humana Dental PPO', carrier_name: 'Humana Dental', effective_date: '2020-01-01' },
        
        // United Concordia relationships
        { network_name: 'United Concordia PPO', carrier_name: 'United Concordia', effective_date: '2020-01-01', verification_required: false },
        { network_name: 'United Concordia PPO', carrier_name: 'United Concordia Companies, Inc.', effective_date: '2020-01-01' },
        
        // Guardian relationships
        { network_name: 'Guardian DentalGuard', carrier_name: 'Guardian', effective_date: '2020-01-01', verification_required: false },
        
        // BCBS relationships (major states)
        { network_name: 'Blue Cross Blue Shield Dental', carrier_name: 'Blue Cross Blue Shield Massachusetts', effective_date: '2020-01-01' },
        { network_name: 'Blue Cross Blue Shield Dental', carrier_name: 'Blue Cross Blue Shield of California', effective_date: '2020-01-01' },
        { network_name: 'Blue Cross Blue Shield Dental', carrier_name: 'Blue Cross Blue Shield of Texas', effective_date: '2020-01-01' },
        { network_name: 'Blue Cross Blue Shield Dental', carrier_name: 'Blue Cross Blue Shield of Florida', effective_date: '2020-01-01' },
        { network_name: 'Blue Cross Blue Shield Dental', carrier_name: 'Blue Cross Blue Shield of New York', effective_date: '2020-01-01' },
        
        // Anthem relationships
        { network_name: 'Anthem Dental PPO', carrier_name: 'Anthem', effective_date: '2020-01-01', verification_required: false },
        { network_name: 'Anthem Dental PPO', carrier_name: 'ANTHEM BCBS', effective_date: '2020-01-01' },
        { network_name: 'Blue Cross Blue Shield Dental', carrier_name: 'Anthem Blue Cross Blue Shield', effective_date: '2020-01-01' },
        
        // Federal programs
        { network_name: 'FEDVIP Dental', carrier_name: 'Delta Dental', effective_date: '2020-01-01', special_notes: 'Federal employee program' },
        { network_name: 'FEDVIP Dental', carrier_name: 'United Concordia', effective_date: '2020-01-01', special_notes: 'Federal employee program' },
        { network_name: 'GEHA Dental', carrier_name: 'Delta Dental', effective_date: '2020-01-01', special_notes: 'Government employees only' }
      ];

      // Get network and carrier IDs
      const networkIds = new Map<string, number>();
      const carrierIds = new Map<string, number>();
      
      const networksResult = await client.query('SELECT id, network_name FROM insurance_networks');
      for (const row of networksResult.rows) {
        networkIds.set(row.network_name, row.id);
      }
      
      const carriersResult = await client.query('SELECT id, carrier_name FROM insurance_carriers');
      for (const row of carriersResult.rows) {
        carrierIds.set(row.carrier_name, row.id);
      }

      // Insert relationships
      let insertedCount = 0;
      let skippedCount = 0;
      
      for (const rel of relationships) {
        const networkId = networkIds.get(rel.network_name);
        const carrierId = carrierIds.get(rel.carrier_name);
        
        if (!networkId) {
          console.log(`  ⚠️  Network not found: ${rel.network_name}`);
          skippedCount++;
          continue;
        }
        
        if (!carrierId) {
          console.log(`  ⚠️  Carrier not found: ${rel.carrier_name}`);
          skippedCount++;
          continue;
        }
        
        try {
          await client.query(`
            INSERT INTO network_carrier_relationships (
              network_id, carrier_id, effective_date, termination_date, 
              special_notes, verification_required, created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
            ON CONFLICT (network_id, carrier_id) DO UPDATE SET
              effective_date = EXCLUDED.effective_date,
              special_notes = EXCLUDED.special_notes,
              verification_required = EXCLUDED.verification_required,
              updated_at = NOW()
          `, [
            networkId,
            carrierId,
            rel.effective_date || null,
            rel.termination_date || null,
            rel.special_notes || null,
            rel.verification_required || false
          ]);
          insertedCount++;
        } catch (error) {
          console.log(`  ❌ Error inserting relationship: ${rel.network_name} - ${rel.carrier_name}`);
          skippedCount++;
        }
      }

      console.log(`  ✅ Created ${insertedCount} network relationships, skipped ${skippedCount}`);
      
    } finally {
      await client.end();
    }
  }
}

// Run the script
if (require.main === module) {
  const manager = new NetworkRelationshipManager();
  manager.run().catch(console.error);
}

export default NetworkRelationshipManager;
